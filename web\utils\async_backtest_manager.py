#!/usr/bin/env python3
"""
异步回测管理器
Async Backtest Manager

提供异步回测任务管理功能，支持后台执行、进度跟踪和结果通知
"""

import asyncio
import threading
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
import sys
import os

# 导入数据存储
try:
    from .data_store import data_store
except ImportError:
    data_store = None

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from src.strategies.ema_dynamic_strategy import EMADynamicStrategy
    from src.data_layer.historical_data_fetcher import scan_local_databases
    from backtest_visualization import BacktestVisualizer
    from src.utils.output_formatter import LogCapture
except ImportError as e:
    print(f"导入模块失败: {e}")

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消

class BacktestTask:
    """回测任务类"""
    
    def __init__(self, task_id: str, config: Dict[str, Any]):
        self.task_id = task_id
        self.config = config
        self.status = TaskStatus.PENDING
        self.progress = 0.0
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.result: Optional[Dict[str, Any]] = None
        self.error: Optional[str] = None
        self.log_messages: List[str] = []
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'config': self.config,
            'status': self.status.value,
            'progress': self.progress,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'result': self.result,
            'error': self.error,
            'log_messages': self.log_messages[-10:]  # 只保留最近10条日志
        }

class AsyncBacktestManager:
    """异步回测管理器 - 增强版跨页面数据共享"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式确保全局唯一实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(AsyncBacktestManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return

        self.tasks: Dict[str, BacktestTask] = {}
        self.executor = None
        self.callbacks: List[Callable] = []
        self.data_dir = project_root / "data" / "tasks"
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # 增强的三重加载机制
        self._load_tasks_from_file()
        self._load_from_session_state()
        self._load_from_data_store()

        # 强制同步所有数据源
        if data_store:
            data_store.force_sync_all_sources()

        self._initialized = True
        
    def add_callback(self, callback: Callable):
        """添加状态变化回调函数"""
        self.callbacks.append(callback)
        
    def _notify_callbacks(self, task: BacktestTask):
        """通知所有回调函数"""
        for callback in self.callbacks:
            try:
                callback(task)
            except Exception as e:
                print(f"回调函数执行失败: {e}")

        # 自动保存任务数据（三重保险）
        self._save_tasks_to_file()
        self._save_to_session_state()
        self._save_to_data_store(task)
    
    def create_task(self, config: Dict[str, Any]) -> str:
        """
        创建新的回测任务

        Args:
            config: 回测配置参数

        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        task = BacktestTask(task_id, config)
        self.tasks[task_id] = task

        # 双重保存
        self._save_tasks_to_file()
        self._save_to_session_state()

        return task_id
    
    def start_task(self, task_id: str) -> bool:
        """
        启动回测任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功启动
        """
        if task_id not in self.tasks:
            return False
            
        task = self.tasks[task_id]
        if task.status != TaskStatus.PENDING:
            return False
        
        # 在新线程中执行回测
        thread = threading.Thread(target=self._run_backtest, args=(task,))
        thread.daemon = True
        thread.start()
        
        return True
    
    def _run_backtest(self, task: BacktestTask):
        """
        执行回测任务（在后台线程中运行）
        
        Args:
            task: 回测任务对象
        """
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.start_time = datetime.now()
            task.progress = 0.0
            self._notify_callbacks(task)
            
            # 解析配置参数
            config = task.config
            symbols = config.get('symbols', ['BTCUSDT'])
            initial_capital = config.get('initial_capital', 10000)
            strategy_params = config.get('strategy_params', {})
            start_date = config.get('start_date')
            end_date = config.get('end_date')
            enable_partial_profit = config.get('enable_partial_profit', False)
            verbose_level = config.get('verbose_level', 1)
            
            task.log_messages.append(f"开始回测: {symbols}")
            task.progress = 10.0
            self._notify_callbacks(task)
            
            # 扫描数据库
            db_info = scan_local_databases()
            if not db_info:
                raise Exception("未找到可用的数据库")
            
            task.log_messages.append(f"找到数据库: {len(db_info)}个")
            task.progress = 20.0
            self._notify_callbacks(task)
            
            # 加载数据
            symbol_data = {}
            for i, symbol in enumerate(symbols):
                try:
                    data = self._load_symbol_data(symbol, db_info[0], start_date, end_date)
                    if data is not None and len(data) > 0:
                        symbol_data[symbol] = data
                        task.log_messages.append(f"加载 {symbol} 数据: {len(data)}条")
                    else:
                        task.log_messages.append(f"警告: {symbol} 数据为空")
                except Exception as e:
                    task.log_messages.append(f"加载 {symbol} 失败: {e}")
                
                # 更新进度
                progress = 20.0 + (i + 1) / len(symbols) * 30.0
                task.progress = progress
                self._notify_callbacks(task)
            
            if not symbol_data:
                raise Exception("没有可用的数据进行回测")
            
            task.log_messages.append(f"数据加载完成，开始策略回测")
            task.progress = 50.0
            self._notify_callbacks(task)
            
            # 创建策略实例
            strategy = EMADynamicStrategy(
                strategy_params=strategy_params,
                initial_capital=initial_capital,
                enable_partial_profit=enable_partial_profit,
                verbose_level=verbose_level
            )
            
            # 执行回测
            results = strategy.simulate_real_time_trading(symbol_data)
            
            task.log_messages.append("回测执行完成，生成结果")
            task.progress = 80.0
            self._notify_callbacks(task)
            
            # 处理结果
            task.result = self._process_results(results, config)
            
            task.log_messages.append("回测任务完成")
            task.progress = 100.0
            task.status = TaskStatus.COMPLETED
            task.end_time = datetime.now()
            self._notify_callbacks(task)
            
        except Exception as e:
            task.error = str(e)
            task.status = TaskStatus.FAILED
            task.end_time = datetime.now()
            task.log_messages.append(f"回测失败: {e}")
            self._notify_callbacks(task)
    
    def _load_symbol_data(self, symbol: str, db_info: dict, 
                         start_date: str = None, end_date: str = None):
        """加载单个币种的数据"""
        try:
            import sqlite3
            import pandas as pd
            
            # 连接数据库
            conn = sqlite3.connect(str(db_info['db_file']))
            
            # 构建查询
            query = f"""
            SELECT datetime_str as datetime, open_price as open, high_price as high,
                   low_price as low, close_price as close, volume
            FROM {db_info['table_name']}
            WHERE symbol = '{symbol}'
            """
            
            if start_date:
                query += f" AND datetime_str >= '{start_date}'"
            if end_date:
                query += f" AND datetime_str <= '{end_date}'"
                
            query += " ORDER BY datetime_str"
            
            # 执行查询
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            if len(data) == 0:
                return None
                
            # 处理数据格式
            data['datetime'] = pd.to_datetime(data['datetime'])
            data.set_index('datetime', inplace=True)
            
            # 确保数据类型正确
            for col in ['open', 'high', 'low', 'close', 'volume']:
                data[col] = pd.to_numeric(data[col], errors='coerce')
            
            return data
            
        except Exception as e:
            print(f"加载 {symbol} 数据失败: {e}")
            return None
    
    def _process_results(self, results: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """处理回测结果"""
        try:
            # 提取关键指标
            processed_results = {
                'config': config,
                'summary': {
                    'total_trades': len(results.get('trades', [])),
                    'profitable_trades': len([t for t in results.get('trades', []) if t.get('pnl', 0) > 0]),
                    'total_return': results.get('total_return', 0),
                    'final_capital': results.get('final_capital', config.get('initial_capital', 10000)),
                    'max_drawdown': results.get('max_drawdown', 0),
                    'sharpe_ratio': results.get('sharpe_ratio', 0),
                    'win_rate': 0
                },
                'trades': results.get('trades', []),
                'equity_curve': results.get('equity_curve', []),
                'performance_metrics': results.get('performance_metrics', {}),
                'timestamp': datetime.now().isoformat()
            }
            
            # 计算胜率
            total_trades = processed_results['summary']['total_trades']
            if total_trades > 0:
                processed_results['summary']['win_rate'] = (
                    processed_results['summary']['profitable_trades'] / total_trades * 100
                )
            
            return processed_results
            
        except Exception as e:
            return {
                'error': f"结果处理失败: {e}",
                'raw_results': results,
                'timestamp': datetime.now().isoformat()
            }
    
    def get_task(self, task_id: str) -> Optional[BacktestTask]:
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务信息"""
        return [task.to_dict() for task in self.tasks.values()]
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False
            
        task = self.tasks[task_id]
        if task.status == TaskStatus.RUNNING:
            # 注意：实际的取消逻辑需要更复杂的实现
            task.status = TaskStatus.CANCELLED
            task.end_time = datetime.now()
            self._notify_callbacks(task)
            return True
        
        return False
    
    def clear_completed_tasks(self):
        """清理已完成的任务"""
        completed_tasks = [
            task_id for task_id, task in self.tasks.items()
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
        ]

        for task_id in completed_tasks:
            del self.tasks[task_id]

        # 双重保存
        self._save_tasks_to_file()
        self._save_to_session_state()

    def get_completed_tasks(self) -> List[BacktestTask]:
        """获取所有已完成的任务 - 增强版本，确保跨页面数据同步"""
        # 多重数据源加载确保数据最新
        self._refresh_all_data_sources()

        return [
            task for task in self.tasks.values()
            if task.status == TaskStatus.COMPLETED and task.result is not None
        ]

    def get_all_tasks(self) -> Dict[str, BacktestTask]:
        """获取所有任务 - 增强版本，确保跨页面数据同步"""
        # 多重数据源加载确保数据最新
        self._refresh_all_data_sources()
        return self.tasks.copy()

    def _refresh_all_data_sources(self):
        """刷新所有数据源，确保数据最新"""
        try:
            # 重新加载所有数据源
            self._load_tasks_from_file()
            self._load_from_session_state()
            self._load_from_data_store()

            # 强制同步数据存储
            if data_store:
                data_store.force_sync_all_sources()

        except Exception as e:
            print(f"❌ 刷新数据源失败: {e}")

    def _save_to_data_store(self, task: BacktestTask):
        """保存到全局数据存储"""
        if data_store:
            try:
                task_data = task.to_dict()
                data_store.add_task(task.task_id, task_data)
                print(f"✅ 任务保存到数据存储: {task.task_id}")
            except Exception as e:
                print(f"❌ 数据存储保存失败: {e}")

    def _load_from_data_store(self):
        """从全局数据存储加载"""
        if data_store:
            try:
                completed_tasks = data_store.get_completed_tasks()
                for task_data in completed_tasks:
                    task_id = task_data['task_id']
                    if task_id not in self.tasks:
                        task = BacktestTask(task_id, task_data['config'])
                        task.status = TaskStatus.COMPLETED
                        task.result = task_data['result']
                        if task_data.get('start_time'):
                            task.start_time = datetime.fromisoformat(task_data['start_time'])
                        if task_data.get('end_time'):
                            task.end_time = datetime.fromisoformat(task_data['end_time'])
                        self.tasks[task_id] = task

                print(f"✅ 从数据存储加载了 {len(completed_tasks)} 个任务")
            except Exception as e:
                print(f"❌ 数据存储加载失败: {e}")

    def _save_tasks_to_file(self):
        """保存任务数据到文件"""
        try:
            # 确保目录存在
            self.data_dir.mkdir(parents=True, exist_ok=True)

            tasks_file = self.data_dir / "tasks.json"
            tasks_data = {}

            for task_id, task in self.tasks.items():
                tasks_data[task_id] = task.to_dict()

            # 使用绝对路径和简化的写入方式
            with open(str(tasks_file), 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)

            # 验证文件是否真的被创建
            if tasks_file.exists():
                print(f"✅ 任务数据保存成功: {tasks_file} ({len(tasks_data)}个任务)")
            else:
                print(f"❌ 文件保存失败: {tasks_file}")

        except Exception as e:
            print(f"❌ 保存任务数据失败: {e}")
            import traceback
            traceback.print_exc()

    def _save_to_session_state(self):
        """保存到Streamlit session_state作为备选方案"""
        try:
            import streamlit as st
            if hasattr(st, 'session_state'):
                tasks_data = {}
                for task_id, task in self.tasks.items():
                    tasks_data[task_id] = task.to_dict()
                st.session_state['backtest_tasks'] = tasks_data
                print(f"✅ 任务数据保存到session_state: {len(tasks_data)}个任务")
        except Exception as e:
            print(f"❌ session_state保存失败: {e}")

    def _load_from_session_state(self):
        """从Streamlit session_state加载数据"""
        try:
            import streamlit as st
            if hasattr(st, 'session_state') and 'backtest_tasks' in st.session_state:
                tasks_data = st.session_state['backtest_tasks']

                for task_id, task_dict in tasks_data.items():
                    if task_id not in self.tasks:  # 避免重复加载
                        task = BacktestTask(task_id, task_dict['config'])
                        task.status = TaskStatus(task_dict['status'])
                        task.progress = task_dict['progress']
                        task.result = task_dict['result']
                        task.error = task_dict['error']
                        task.log_messages = task_dict['log_messages']

                        # 处理时间字段
                        if task_dict['start_time']:
                            task.start_time = datetime.fromisoformat(task_dict['start_time'])
                        if task_dict['end_time']:
                            task.end_time = datetime.fromisoformat(task_dict['end_time'])

                        self.tasks[task_id] = task

                print(f"✅ 从session_state加载了 {len(tasks_data)} 个任务")

        except Exception as e:
            print(f"❌ session_state加载失败: {e}")

    def _load_tasks_from_file(self):
        """从文件加载任务数据"""
        try:
            tasks_file = self.data_dir / "tasks.json"

            print(f"[DEBUG] 尝试加载任务数据从: {tasks_file}")

            if not tasks_file.exists():
                print(f"[DEBUG] 任务文件不存在: {tasks_file}")
                return

            print(f"[DEBUG] 任务文件存在，开始加载")

            with open(tasks_file, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)

            print(f"[DEBUG] 从文件加载了 {len(tasks_data)} 个任务")

            for task_id, task_dict in tasks_data.items():
                task = BacktestTask(task_id, task_dict['config'])
                task.status = TaskStatus(task_dict['status'])
                task.progress = task_dict['progress']
                task.result = task_dict['result']
                task.error = task_dict['error']
                task.log_messages = task_dict['log_messages']

                # 处理时间字段
                if task_dict['start_time']:
                    task.start_time = datetime.fromisoformat(task_dict['start_time'])
                if task_dict['end_time']:
                    task.end_time = datetime.fromisoformat(task_dict['end_time'])

                self.tasks[task_id] = task
                print(f"[DEBUG] 加载任务: {task_id} - {task.status.value}")

        except Exception as e:
            print(f"[ERROR] 加载任务数据失败: {e}")
            import traceback
            traceback.print_exc()

# 全局单例实例
async_backtest_manager = AsyncBacktestManager()
