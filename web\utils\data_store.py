#!/usr/bin/env python3
"""
全局数据存储管理器
Global Data Store Manager

提供跨页面的数据共享功能，确保异步回测数据在所有页面间正确同步
"""

import json
import os
import threading
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

class DataStore:
    """全局数据存储管理器 - 增强版跨页面数据共享"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式确保全局唯一实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DataStore, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return

        self.data_dir = Path(__file__).parent.parent.parent / "data"
        self.data_dir.mkdir(exist_ok=True)
        self.tasks_file = self.data_dir / "backtest_tasks.json"
        self.session_file = self.data_dir / "session_data.json"
        self._initialized = True
    
    def save_tasks(self, tasks_data: Dict[str, Any]) -> bool:
        """保存任务数据 - 增强版本，支持原子写入"""
        try:
            # 使用临时文件确保原子写入
            temp_file = self.tasks_file.with_suffix('.tmp')

            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False, default=str)

            # 原子替换
            temp_file.replace(self.tasks_file)

            # 同时保存到session文件作为备份
            self._save_session_backup(tasks_data)

            print(f"✅ 任务数据保存成功: {len(tasks_data)}个任务")
            return True
        except Exception as e:
            print(f"❌ 保存任务数据失败: {e}")
            return False
    
    def load_tasks(self) -> Dict[str, Any]:
        """加载任务数据 - 增强版本，支持多重数据源"""
        tasks_data = {}

        # 方法1: 从主文件加载
        try:
            if self.tasks_file.exists():
                with open(self.tasks_file, 'r', encoding='utf-8') as f:
                    tasks_data = json.load(f)
                print(f"✅ 从主文件加载了 {len(tasks_data)} 个任务")
        except Exception as e:
            print(f"❌ 从主文件加载失败: {e}")

        # 方法2: 从session备份文件加载（如果主文件为空或损坏）
        if not tasks_data:
            try:
                if self.session_file.exists():
                    with open(self.session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                        tasks_data = session_data.get('tasks', {})
                    print(f"✅ 从备份文件加载了 {len(tasks_data)} 个任务")
            except Exception as e:
                print(f"❌ 从备份文件加载失败: {e}")

        # 方法3: 从Streamlit session_state加载（最后的备选方案）
        if not tasks_data:
            try:
                import streamlit as st
                if hasattr(st, 'session_state') and 'backtest_tasks' in st.session_state:
                    st_tasks = st.session_state['backtest_tasks']
                    if isinstance(st_tasks, dict):
                        tasks_data = st_tasks
                        print(f"✅ 从session_state加载了 {len(tasks_data)} 个任务")
            except Exception as e:
                print(f"❌ 从session_state加载失败: {e}")

        return tasks_data
    
    def get_completed_tasks(self) -> List[Dict[str, Any]]:
        """获取已完成的任务"""
        tasks = self.load_tasks()
        completed = []
        
        for task_id, task_data in tasks.items():
            if (task_data.get('status') == 'completed' and 
                task_data.get('result') is not None):
                completed.append({
                    'task_id': task_id,
                    'config': task_data.get('config', {}),
                    'result': task_data.get('result', {}),
                    'start_time': task_data.get('start_time'),
                    'end_time': task_data.get('end_time')
                })
        
        return completed
    
    def add_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """添加或更新任务"""
        try:
            tasks = self.load_tasks()
            tasks[task_id] = task_data
            return self.save_tasks(tasks)
        except Exception as e:
            print(f"添加任务失败: {e}")
            return False
    
    def update_task_status(self, task_id: str, status: str,
                          result: Optional[Dict[str, Any]] = None) -> bool:
        """更新任务状态 - 增强版本，支持跨页面同步"""
        try:
            tasks = self.load_tasks()
            if task_id in tasks:
                tasks[task_id]['status'] = status
                if result:
                    tasks[task_id]['result'] = result
                if status == 'completed':
                    tasks[task_id]['end_time'] = datetime.now().isoformat()

                # 保存到所有数据源
                success = self.save_tasks(tasks)
                if success:
                    self._sync_to_session_state(tasks)
                return success
            return False
        except Exception as e:
            print(f"❌ 更新任务状态失败: {e}")
            return False

    def _save_session_backup(self, tasks_data: Dict[str, Any]):
        """保存session备份文件"""
        try:
            session_data = {
                'tasks': tasks_data,
                'timestamp': datetime.now().isoformat(),
                'version': '1.0'
            }
            with open(self.session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            print(f"❌ 保存session备份失败: {e}")

    def _sync_to_session_state(self, tasks_data: Dict[str, Any]):
        """同步到Streamlit session_state"""
        try:
            import streamlit as st
            if hasattr(st, 'session_state'):
                st.session_state['backtest_tasks'] = tasks_data
                print(f"✅ 同步到session_state: {len(tasks_data)}个任务")
        except Exception as e:
            print(f"❌ 同步到session_state失败: {e}")

    def force_sync_all_sources(self):
        """强制同步所有数据源"""
        try:
            tasks = self.load_tasks()
            if tasks:
                self.save_tasks(tasks)
                print(f"✅ 强制同步完成: {len(tasks)}个任务")
            return True
        except Exception as e:
            print(f"❌ 强制同步失败: {e}")
            return False

# 全局单例实例
data_store = DataStore()
